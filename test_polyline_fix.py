#!/usr/bin/env python3
"""
Test script to verify the polyline area results fix.
This script tests the key components that were modified.
"""

import numpy as np
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_descriptor_validation():
    """Test the new descriptor validation logic"""
    print("Testing descriptor validation...")
    
    # Test case 1: Valid descriptor with some data
    valid_desc = {
        'data': np.array([1, 2, 3], dtype=np.float32),
        'hfc': np.array([0.1, 0.2, 0.3], dtype=np.float32),
        'norm_fdom': np.array([1.0, 2.0, 3.0], dtype=np.float32)
    }
    
    # Test case 2: Descriptor with missing keys but some valid data
    partial_desc = {
        'data': np.array([1, 2, 3], dtype=np.float32),
        'hfc': np.array([0.1, 0.2, 0.3], dtype=np.float32)
        # Missing norm_fdom and mag_voice_slope
    }
    
    # Test case 3: Descriptor with all NaN values
    nan_desc = {
        'data': np.array([np.nan, np.nan, np.nan], dtype=np.float32),
        'hfc': np.array([np.nan, np.nan, np.nan], dtype=np.float32)
    }
    
    # Test case 4: Empty descriptor
    empty_desc = {}
    
    # Test case 5: Error descriptor (old format)
    error_desc = {
        'error': 'GPU processing failed',
        'well_marker_name': 'Test Well',
        'trace_idx': 0
    }
    
    def validate_descriptor(desc):
        """Simplified version of the new validation logic"""
        if not desc or not isinstance(desc, dict):
            return False, "Not a dictionary"
        
        if 'error' in desc:
            return False, "Contains error"
        
        # Check if descriptor has any valid numeric data
        has_valid_data = False
        for key, value in desc.items():
            if isinstance(value, np.ndarray) and value.size > 0:
                if np.any(np.isfinite(value)):
                    has_valid_data = True
                    break
            elif isinstance(value, (int, float, np.number)) and np.isfinite(value):
                has_valid_data = True
                break
        
        return has_valid_data, "Valid data found" if has_valid_data else "No valid data"
    
    test_cases = [
        ("Valid descriptor", valid_desc),
        ("Partial descriptor", partial_desc),
        ("NaN descriptor", nan_desc),
        ("Empty descriptor", empty_desc),
        ("Error descriptor", error_desc)
    ]
    
    for name, desc in test_cases:
        is_valid, reason = validate_descriptor(desc)
        print(f"  {name}: {'VALID' if is_valid else 'INVALID'} - {reason}")
    
    print("Descriptor validation test completed.\n")

def test_woss_calculation():
    """Test WOSS calculation with missing keys"""
    print("Testing WOSS calculation...")
    
    try:
        from utils.processing import calculate_woss
        
        # Test case 1: Complete descriptor
        complete_desc = {
            'hfc': np.array([0.1, 0.2, 0.3], dtype=np.float32),
            'norm_fdom': np.array([1.0, 2.0, 3.0], dtype=np.float32),
            'mag_voice_slope': np.array([0.5, 1.0, 1.5], dtype=np.float32)
        }
        
        # Test case 2: Incomplete descriptor
        incomplete_desc = {
            'hfc': np.array([0.1, 0.2, 0.3], dtype=np.float32),
            'norm_fdom': np.array([1.0, 2.0, 3.0], dtype=np.float32)
            # Missing mag_voice_slope
        }
        
        plot_settings = {
            'epsilon': 1e-4,
            'fdom_exponent': 2.0,
            'hfc_p95': 1.0
        }
        
        # Test complete descriptor
        try:
            woss_complete = calculate_woss(complete_desc, plot_settings)
            print(f"  Complete descriptor WOSS: {woss_complete.shape} - SUCCESS")
        except Exception as e:
            print(f"  Complete descriptor WOSS: FAILED - {e}")
        
        # Test incomplete descriptor
        try:
            woss_incomplete = calculate_woss(incomplete_desc, plot_settings)
            print(f"  Incomplete descriptor WOSS: {woss_incomplete.shape} - SUCCESS (should return zeros)")
        except Exception as e:
            print(f"  Incomplete descriptor WOSS: FAILED - {e}")
            
    except ImportError as e:
        print(f"  Could not import calculate_woss: {e}")
    
    print("WOSS calculation test completed.\n")

def test_gpu_import():
    """Test GPU function import"""
    print("Testing GPU function import...")
    
    try:
        from utils.dlogst_spec_descriptor_gpu import dlogst_spec_descriptor_gpu
        print("  GPU function import: SUCCESS")
        
        # Test basic function call with minimal data
        test_data = np.array([1, 2, 3, 4, 5], dtype=np.float32)
        dt = 0.001
        
        try:
            result = dlogst_spec_descriptor_gpu(test_data, dt)
            print(f"  GPU function call: SUCCESS - returned {type(result)}")
            if isinstance(result, dict):
                print(f"    Keys: {list(result.keys())}")
        except Exception as e:
            print(f"  GPU function call: FAILED - {e}")
            
    except ImportError as e:
        print(f"  GPU function import: FAILED - {e}")
    
    print("GPU function import test completed.\n")

def main():
    """Run all tests"""
    print("=== Polyline Area Results Fix Test ===\n")
    
    test_descriptor_validation()
    test_woss_calculation()
    test_gpu_import()
    
    print("=== All tests completed ===")

if __name__ == "__main__":
    main()
