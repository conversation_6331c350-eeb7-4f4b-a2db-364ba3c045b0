# Plan to Fix Session State Handoff Issue

## 1. Summary of the Problem

The WOSS Seismic Analysis Tool fails at "Step 5: Export Results" after a successful analysis in "Step 4: Analyze Data". The error message "Found 0 valid results" indicates that the calculated spectral descriptors from Step 4 are not being correctly passed to Step 5.

This issue specifically affects the following analysis modes:
-   By well markers (Individual well)
-   By well markers (Well Grouping)
-   By polyline

The `Inline` and `Crossline` modes are unaffected.

## 2. Root Cause Analysis

The investigation revealed a data handoff issue within Streamlit's session state management.

-   **Step 4 (Analysis):** The affected analysis modes calculate results and store them in mode-specific session state variables:
    -   `st.session_state.individual_well_analysis_results`
    -   `st.session_state.grouped_well_analysis_results`
    -   `st.session_state.polyline_analysis_results`
-   **Step 5 (Results View):** The results page (`pages/export_results_page.py`) is hardcoded to look for a single, generic session state variable: `st.session_state.calculated_descriptors`.

The bug occurs because the application fails to copy the results from the mode-specific variables into the generic `calculated_descriptors` variable that the next step expects.

## 3. The Solution

The solution is to restore the application's original design pattern by ensuring all analysis modes consolidate their results into the `st.session_state.calculated_descriptors` variable upon completion.

This will be achieved by making small additions to `pages/analyze_data_page.py`.

### 3.1. Implementation Steps

The following lines of code must be added to the `render()` function in `pages/analyze_data_page.py`.

1.  **Fix for "Individual well" analysis:**
    -   **Location:** At the end of the `try` block for individual well analysis (around line 1280).
    -   **Code to add:**
        ```python
        st.session_state.calculated_descriptors = st.session_state.individual_well_analysis_results
        ```

2.  **Fix for "Well Grouping" analysis:**
    -   **Location:** At the end of the `with st.spinner(...)` block for grouped well analysis (around line 1771).
    -   **Code to add:**
        ```python
        st.session_state.calculated_descriptors = st.session_state.grouped_well_analysis_results
        ```

3.  **Fix for "By polyline" analysis:**
    -   **Location:** At the end of the `with st.spinner(...)` block for polyline descriptor calculation (around line 2480).
    -   **Code to add:**
        ```python
        st.session_state.calculated_descriptors = st.session_state.polyline_analysis_results
        ```

### 3.2. Data Flow Diagram

This diagram illustrates the fix:

```mermaid
graph TD
    subgraph "Step 4: Analyze Data (All Modes)"
        A[Analysis Logic] --> B(Results in mode-specific variables);
        B --> C{e.g., individual_well_analysis_results};
        B --> D{e.g., grouped_well_analysis_results};
        B --> E{e.g., polyline_analysis_results};
    end

    subgraph "The Fix"
        F(Add Code) -- "st.session_state.calculated_descriptors = ..." --> G[Consolidated Result in 'calculated_descriptors'];
    end

    subgraph "Step 5: View Results"
        H[Display Logic] --> I{Reads ONLY from `calculated_descriptors`};
    end

    C --> F;
    D --> F;
    E --> F;
    G --> H;
```

This plan ensures a robust and comprehensive solution that aligns all analysis modes with the application's intended data flow, resolving the bug for all affected scenarios.