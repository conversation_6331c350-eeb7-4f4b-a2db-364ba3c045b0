# Inline/Xline Selection Fix Guide for WOSS Application

## Overview

This guide provides step-by-step instructions to fix the inline/xline selection functionality in the WOSS Seismic Analysis Tool. The current implementation has several issues that prevent seamless operation compared to the original backup implementation.

## Identified Issues

### 1. **Workflow Fragmentation**
- Current implementation requires multiple button clicks and page transitions
- Original implementation had a more streamlined flow within a single page
- Processing logic is split between `select_area_page.py` and `analyze_data_page.py`

### 2. **State Management Issues**
- Inconsistent use of session state variables (`selected_inline` vs `selected_inline_number`)
- Missing state initialization for trace loading flags
- Duplicate trace loading logic in multiple places

### 3. **GPU Processing Integration**
- GPU availability checks are inconsistent
- Batch size configuration appears multiple times
- Missing proper error handling for GPU operations

### 4. **UI/UX Problems**
- Users must navigate between pages to complete inline/xline analysis
- Progress indicators and status messages are not consistent
- Missing immediate feedback after selections

## Step-by-Step Fix Implementation

### Step 1: Update Session State Initialization in `app.py`

```python
# In app.py, ensure all necessary session state variables are initialized
# Add these to the initialize_session_state function or at the module level

if 'traces_loaded_inline' not in st.session_state:
    st.session_state.traces_loaded_inline = False
if 'traces_loaded_crossline' not in st.session_state:
    st.session_state.traces_loaded_crossline = False
if 'inline_descriptors_calculated' not in st.session_state:
    st.session_state.inline_descriptors_calculated = False
if 'crossline_descriptors_calculated' not in st.session_state:
    st.session_state.crossline_descriptors_calculated = False
```

### Step 2: Consolidate Inline/Xline Processing in `select_area_page.py`

#### 2.1 Remove the separate trace loading button and integrate it into the flow:

```python
# In select_area_page.py, modify the Single Inline Mode section

elif st.session_state.selection_mode == "Single inline (all crosslines)":
    st.subheader("Single Inline Mode")
    if st.session_state.header_loader:
        import numpy as np
        # Get min and max inline numbers
        min_inline = int(np.min(st.session_state.header_loader.inlines))
        max_inline = int(np.max(st.session_state.header_loader.inlines))

        # Default to previously selected inline or min inline
        default_inline = st.session_state.selected_inline if st.session_state.selected_inline else min_inline

        # Inline number selection
        selected_inline = st.number_input(
            f"Specify an inline number ({min_inline}-{max_inline}):",
            min_value=min_inline,
            max_value=max_inline,
            value=default_inline,
            step=1,
            key="inline_number_input"
        )
        
        # Update session state only if value changed
        if selected_inline != st.session_state.selected_inline:
            st.session_state.selected_inline = selected_inline
            st.session_state.traces_loaded_inline = False  # Reset trace loading flag
            st.session_state.inline_descriptors_calculated = False

        # Batch size selection if GPU is available
        if st.session_state.get('GPU_AVAILABLE', False):
            suggested_batch, free_mb = get_suggested_batch_size()
            st.info(f"Estimated free GPU memory: {free_mb:.1f} MB. Suggested batch size: {suggested_batch}")
            st.session_state.batch_size = st.number_input(
                "Batch size for GPU processing:",
                min_value=10,
                max_value=4000,
                value=suggested_batch,
                step=10,
                help="Number of traces to process at once. Higher values use more GPU memory."
            )
        else:
            st.warning("GPU processing not available. Processing will be slower.")
            st.session_state.batch_size = None

        # Automatically proceed to find traces
        inline_mask = st.session_state.header_loader.inlines == st.session_state.selected_inline
        chosen_indices = st.session_state.header_loader.unique_indices[inline_mask]
        
        if len(chosen_indices) == 0:
            st.error(f"No traces found for inline {st.session_state.selected_inline}")
        else:
            st.success(f"Found {len(chosen_indices)} traces for inline {st.session_state.selected_inline}")
            st.session_state.selected_indices = chosen_indices.tolist()
            st.session_state.area_selected = True
            st.session_state.area_selected_mode = 'inline'
            
            # Add proceed button that goes directly to analysis
            if st.button("Proceed to Analysis", key="inline_proceed_button"):
                st.session_state.current_step = "analyze_data"
                st.rerun()
```

#### 2.2 Apply similar changes to Single Crossline Mode:

```python
elif st.session_state.selection_mode == "Single crossline (all inlines)":
    st.subheader("Single Crossline Mode")
    if st.session_state.header_loader:
        import numpy as np
        # Get min and max crossline numbers
        min_crossline = int(np.min(st.session_state.header_loader.crosslines))
        max_crossline = int(np.max(st.session_state.header_loader.crosslines))

        # Default to previously selected crossline or min crossline
        default_crossline = st.session_state.selected_crossline if st.session_state.selected_crossline else min_crossline

        # Crossline number selection
        selected_crossline = st.number_input(
            f"Specify a crossline number ({min_crossline}-{max_crossline}):",
            min_value=min_crossline,
            max_value=max_crossline,
            value=default_crossline,
            step=1,
            key="crossline_number_input"
        )
        
        # Update session state only if value changed
        if selected_crossline != st.session_state.selected_crossline:
            st.session_state.selected_crossline = selected_crossline
            st.session_state.traces_loaded_crossline = False
            st.session_state.crossline_descriptors_calculated = False

        # Batch size selection (same as inline mode)
        # ... (similar code as above)

        # Automatically proceed to find traces
        crossline_mask = st.session_state.header_loader.crosslines == st.session_state.selected_crossline
        chosen_indices = st.session_state.header_loader.unique_indices[crossline_mask]
        
        if len(chosen_indices) == 0:
            st.error(f"No traces found for crossline {st.session_state.selected_crossline}")
        else:
            st.success(f"Found {len(chosen_indices)} traces for crossline {st.session_state.selected_crossline}")
            st.session_state.selected_indices = chosen_indices.tolist()
            st.session_state.area_selected = True
            st.session_state.area_selected_mode = 'crossline'
            
            # Add proceed button
            if st.button("Proceed to Analysis", key="crossline_proceed_button"):
                st.session_state.current_step = "analyze_data"
                st.rerun()
```

### Step 3: Streamline Analysis in `analyze_data_page.py`

#### 3.1 Create a unified function for inline/crossline analysis:

```python
def render_line_analysis(line_type='inline'):
    """Unified function to handle both inline and crossline analysis."""
    
    # Determine which line we're analyzing
    if line_type == 'inline':
        selected_value = st.session_state.selected_inline
        line_name = "inline"
        loaded_flag = 'traces_loaded_inline'
        calculated_flag = 'inline_descriptors_calculated'
    else:
        selected_value = st.session_state.selected_crossline
        line_name = "crossline"
        loaded_flag = 'traces_loaded_crossline'
        calculated_flag = 'crossline_descriptors_calculated'
    
    st.subheader(f"Analysis for {line_name.capitalize()} {selected_value}")
    
    # Check if traces need to be loaded
    if not st.session_state.get(loaded_flag, False):
        with st.spinner(f"Loading traces for {line_name} {selected_value}..."):
            loaded_trace_data = []
            try:
                for trace_idx in st.session_state.selected_indices:
                    trace_sample = load_trace_sample(
                        st.session_state.header_loader.source_file_path, 
                        trace_idx
                    )
                    loaded_trace_data.append({
                        'trace_sample': trace_sample, 
                        'trace_idx': trace_idx
                    })
                
                # Store loaded data
                st.session_state.loaded_trace_data = loaded_trace_data
                st.session_state[loaded_flag] = True
                st.success(f"Successfully loaded {len(loaded_trace_data)} traces.")
                
            except Exception as e:
                st.error(f"Error loading traces: {e}")
                logging.error(f"Error loading traces for {line_name} {selected_value}: {e}", exc_info=True)
                return
    
    # Display loaded trace information
    st.info(f"{len(st.session_state.loaded_trace_data)} traces loaded for {line_name} {selected_value}")
    
    # Select outputs
    st.subheader("Select Outputs")
    available_outputs = AVAILABLE_OUTPUTS_SECTION
    
    default_outputs = st.session_state.get('selected_outputs', ["Input Signal", "WOSS"])
    default_outputs = [output for output in default_outputs if output in available_outputs]
    
    st.session_state.selected_outputs = st.multiselect(
        "Select outputs to display:",
        options=available_outputs,
        default=default_outputs,
        key=f"{line_name}_outputs_multiselect"
    )
    
    if not st.session_state.selected_outputs:
        st.warning("Please select at least one output.")
        return
    
    # Calculate descriptors button
    if st.button("Calculate Descriptors", key=f"calc_desc_{line_name}"):
        calculate_line_descriptors(line_type)
    
    # Show results if calculated
    if st.session_state.get(calculated_flag, False):
        display_line_results(line_type)
```

#### 3.2 Update the main render function in analyze_data_page.py:

```python
def render():
    """Main render function for analyze data page."""
    initialize_session_state()
    
    # ... (existing prerequisite checks)
    
    # Route based on selection mode
    if st.session_state.selection_mode == "By well markers":
        render_well_analysis()
    elif st.session_state.selection_mode == "Single inline (all crosslines)":
        render_line_analysis('inline')
    elif st.session_state.selection_mode == "Single crossline (all inlines)":
        render_line_analysis('crossline')
    elif st.session_state.selection_mode == "By inline/crossline section (AOI)":
        render_aoi_export()
    elif st.session_state.selection_mode == "By Polyline File Import":
        render_polyline_analysis()
```

### Step 4: Fix GPU Function Integration

#### 4.1 Create a centralized GPU processing function in `utils/gpu_utils.py`:

```python
def process_traces_gpu(traces_array, dt, batch_size, descriptor_settings, 
                      outputs_to_calculate, progress_callback=None):
    """
    Centralized GPU processing function for trace descriptors.
    
    Args:
        traces_array: 2D numpy array of traces (traces x samples)
        dt: Sampling interval
        batch_size: GPU batch size
        descriptor_settings: Dictionary of descriptor parameters
        outputs_to_calculate: List of outputs to calculate
        progress_callback: Optional callback for progress updates
        
    Returns:
        Dictionary of calculated descriptors
    """
    try:
        # Filter out params not used by GPU function
        filtered_params = {
            k: v for k, v in descriptor_settings.items() 
            if k not in ['dt', 'epsilon', 'fdom_exponent', 'hfc_p95']
        }
        
        # Ensure required components for WOSS
        if "WOSS" in outputs_to_calculate:
            required_for_woss = ["hfc", "norm_fdom", "mag_voice_slope"]
            for comp in required_for_woss:
                if comp not in outputs_to_calculate:
                    outputs_to_calculate.append(comp)
        
        # Call appropriate GPU function
        if any(out in ['mag', 'mag_voice'] for out in outputs_to_calculate):
            # Use magnitude version if needed
            all_descriptors = dlogst_spec_descriptor_gpu_2d_chunked_mag(
                traces_array,
                dt,
                batch_size=batch_size,
                **filtered_params
            )
        else:
            # Use standard version
            all_descriptors = dlogst_spec_descriptor_gpu_2d_chunked(
                traces_array,
                dt,
                batch_size=batch_size,
                **filtered_params
            )
        
        # Filter to requested outputs
        calculated_descriptors = {
            key: all_descriptors[key] 
            for key in outputs_to_calculate 
            if key in all_descriptors
        }
        
        # Calculate WOSS if requested
        if "WOSS" in outputs_to_calculate and all(
            comp in calculated_descriptors 
            for comp in ["hfc", "norm_fdom", "mag_voice_slope"]
        ):
            calculated_descriptors["WOSS"] = calculate_woss(
                calculated_descriptors["hfc"],
                calculated_descriptors["norm_fdom"],
                calculated_descriptors["mag_voice_slope"]
            )
        
        return calculated_descriptors
        
    except Exception as e:
        logging.error(f"GPU processing error: {e}", exc_info=True)
        raise
```

### Step 5: Update Export Functionality

#### 5.1 Ensure export works seamlessly from inline/xline analysis:

```python
# In export_results_page.py, add support for inline/crossline modes

def prepare_export_data():
    """Prepare data for export based on selection mode."""
    
    if st.session_state.selection_mode in ["Single inline (all crosslines)", 
                                          "Single crossline (all inlines)"]:
        # For line modes, organize data appropriately
        if st.session_state.selection_mode == "Single inline (all crosslines)":
            line_type = "inline"
            line_value = st.session_state.selected_inline
        else:
            line_type = "crossline"
            line_value = st.session_state.selected_crossline
        
        # Create export structure
        export_data = {
            'mode': st.session_state.selection_mode,
            'line_type': line_type,
            'line_value': line_value,
            'descriptors': st.session_state.calculated_descriptors,
            'trace_indices': st.session_state.selected_indices,
            'metadata': {
                'dt': st.session_state.dt,
                'processing_params': st.session_state.plot_settings
            }
        }
        
        return export_data
```

### Step 6: Add Progress Indicators and Better Error Handling

#### 6.1 Create a progress tracking utility:

```python
# In utils/general_utils.py

class ProgressTracker:
    """Utility class for tracking and displaying progress."""
    
    def __init__(self, total_steps, description="Processing"):
        self.total_steps = total_steps
        self.current_step = 0
        self.description = description
        self.progress_bar = st.progress(0)
        self.status_text = st.empty()
        
    def update(self, step=None, message=None):
        """Update progress bar and status message."""
        if step is not None:
            self.current_step = step
        else:
            self.current_step += 1
            
        progress = min(self.current_step / self.total_steps, 1.0)
        self.progress_bar.progress(progress)
        
        if message:
            self.status_text.text(f"{self.description}: {message}")
        else:
            self.status_text.text(
                f"{self.description}: {self.current_step}/{self.total_steps}"
            )
    
    def complete(self, message="Complete!"):
        """Mark progress as complete."""
        self.progress_bar.progress(1.0)
        self.status_text.text(f"{self.description}: {message}")
```

### Step 7: Testing and Validation

#### 7.1 Create test cases for inline/xline selection:

```python
# In a new file: tests/test_inline_xline_selection.py

def test_inline_selection_workflow():
    """Test the complete inline selection workflow."""
    # 1. Initialize session state
    # 2. Load test SEG-Y file
    # 3. Select inline mode
    # 4. Choose an inline number
    # 5. Verify traces are found
    # 6. Process traces
    # 7. Verify descriptors are calculated
    # 8. Test export functionality
    pass

def test_crossline_selection_workflow():
    """Test the complete crossline selection workflow."""
    # Similar to inline test but for crosslines
    pass

def test_gpu_batch_processing():
    """Test GPU batch processing with different batch sizes."""
    # Test various batch sizes
    # Verify memory usage
    # Check for errors
    pass
```

## Implementation Priority

1. **High Priority**:
   - Fix state management issues (Step 1)
   - Consolidate inline/xline processing (Step 2)
   - Streamline analysis workflow (Step 3)

2. **Medium Priority**:
   - Fix GPU function integration (Step 4)
   - Update export functionality (Step 5)

3. **Low Priority**:
   - Add progress indicators (Step 6)
   - Create test cases (Step 7)

## Expected Outcomes

After implementing these fixes:

1. Users can select inline/xline and proceed directly to analysis without multiple page transitions
2. The workflow will be similar to the original backup implementation
3. GPU processing will be more reliable and consistent
4. Export functionality will work seamlessly for all selection modes
5. Better error handling and user feedback throughout the process

## Additional Recommendations

1. **Code Organization**: Consider creating a dedicated module for line-based analysis (inline/crossline) to keep the code modular
2. **Configuration Management**: Store GPU and processing settings in a centralized configuration
3. **Documentation**: Update user documentation to reflect the improved workflow
4. **Performance Monitoring**: Add logging for performance metrics to identify bottlenecks

## Conclusion

These fixes will restore the inline/xline selection functionality to work as smoothly as the original implementation while maintaining the modular architecture of the current codebase. The key is to reduce the number of user interactions required and ensure a seamless flow from selection to analysis to export.