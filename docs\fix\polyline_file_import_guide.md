# Polyline File Import Guide

## Overview

The "By Polyline File Import" option in the WOSS Seismic Analysis Tool allows you to analyze seismic data along a custom path defined by a polyline. This feature is useful for:

- Analyzing data along geological features like faults or horizons
- Creating seismic sections that follow irregular paths
- Investigating areas of interest that don't align with inline/crossline directions

## File Format Requirements

The polyline file should contain X, Y coordinate pairs that define the path. The following formats are supported:

1. **Line-by-line format**: Each line contains an X, Y coordinate pair (space, tab, or comma separated)
   ```
   X1 Y1
   X2 Y2
   X3 Y3
   ...
   ```

2. **Semicolon-separated format**: All coordinates in a single line, with pairs separated by semicolons
   ```
   X1,Y1; X2,Y2; X3,Y3; ...
   ```

## Step-by-Step Usage Guide

### Step 1: Load Data
- Upload your SEG-Y file and well data (if available) in the "Load Data" step
- Ensure the SEG-Y header bytes are correctly configured for proper coordinate extraction

### Step 2: Configure Display
- Set display parameters such as time range, frequency range, and colormap settings
- Configure the HFC percentile value which will be used for WOSS calculations

### Step 3: Select Analysis Mode
1. Choose "By Polyline File Import" from the mode selection dropdown
2. Upload your polyline file using the file uploader
3. Set the appropriate tolerance value (distance threshold for selecting traces near the polyline)
4. Click "Find Traces Along Polyline" to locate traces within the specified tolerance

### Step 4: Analyze Data
1. Click "Load Traces for Processing" to load trace data for the selected traces
2. Select which spectral descriptors you want to calculate and visualize
3. If GPU processing is available, adjust the batch size if needed
4. Click "Calculate Descriptors" to perform the spectral analysis
5. Click "View Results" when the calculation is complete

### Step 5: View Results
- The results page will display 2D attribute sections along your polyline
- Use the tabs to switch between different attributes
- Each plot shows how the selected attribute varies along the polyline path

### Step 6: Export Results (Optional)
- You can export the calculated attributes as SEG-Y files
- Click "Configure Export Options" to set up the export parameters
- The exported files can be downloaded as a ZIP archive

## Tips for Creating Polyline Files

1. **Use appropriate coordinate system**: Ensure your polyline coordinates use the same coordinate system as your seismic data
2. **Start and end points**: Include enough points to ensure the polyline extends beyond the area of interest
3. **Point spacing**: Use a reasonable spacing between points - too few points may miss important features, while too many can slow down processing
4. **File preparation**: You can create polyline files in a text editor or export them from GIS software

## Example Polyline File

```
435678.45 6782345.67
435700.12 6782380.45
435750.78 6782410.23
435800.56 6782425.89
435850.34 6782450.67
```

## Troubleshooting

- **No traces found**: Check that your polyline coordinates are within the seismic survey area and the tolerance is appropriate
- **Poor section quality**: Increase tolerance for more traces or ensure polyline points are properly spaced
- **Processing errors**: For large polylines, try reducing the batch size or selecting a smaller section of the polyline
