# WOSS Seismic Analysis Tool: Refactoring Blueprint

This document provides a detailed architectural analysis of the WOSS Seismic Analysis Tool, designed to guide the refactoring of a monolithic application into a modular, maintainable, and scalable Streamlit-based solution.

## 1. High-Level Architecture Overview

The application is architected as a multi-page Streamlit application, adopting a clear separation of concerns that is fundamental to modern software design. The structure is logically divided into three primary directories: `common`, `pages`, and `utils`, with a central entry point in `app.py`.

*   **`app.py` (Entry Point):** Acts as the central router and application orchestrator. It initializes the application, manages global state (like GPU availability), and controls the navigation flow.
*   **`common/` (Shared Resources):** Contains modules that are globally accessible and provide shared resources. This includes application-wide constants (`constants.py`), session state management (`session_state.py`), and reusable UI components (`ui_elements.py`). This centralization prevents code duplication and ensures consistency.
*   **`pages/` (UI Views):** Each file in this directory corresponds to a distinct step or "page" in the application's workflow (e.g., loading data, configuration, analysis). This modularization of the UI makes each step independent and easier to manage.
*   **`utils/` (Backend Logic):** This directory encapsulates the core business logic and data processing functionalities. It handles tasks like data loading/parsing (`data_utils.py`), complex scientific computations on both CPU and GPU (`dlogst_spec_descriptor_cpu.py`, `dlogst_spec_descriptor_gpu.py`), and data visualization (`visualization.py`). This separation isolates complex logic from the presentation layer, enhancing testability and reusability.

### Request/Response Flow

The application operates on a state-driven, multi-step workflow managed by Streamlit's session state.

1.  **Initialization:** When a user accesses the application, `app.py` is executed. It sets up the page configuration, initializes the session state via `common.session_state.initialize_session_state()`, and checks for GPU availability using functions from `utils.gpu_utils`.
2.  **Navigation & Routing:** The user interacts with a sidebar navigation menu defined in `app.py`. The selection updates the `current_step` variable in `st.session_state`.
3.  **Page Rendering:** `app.py` contains a main routing block that checks the value of `st.session_state.current_step`. Based on this value, it calls the `render()` function from the corresponding module in the `pages/` directory (e.g., if `current_step` is `"load_data"`, it calls `pages.load_data_page.render()`).
4.  **UI Interaction & Logic Execution:** Within a page module (e.g., `pages.load_data_page.py`), user interactions (like file uploads or button clicks) trigger backend functions from the `utils/` directory. For instance, clicking the "Load Data" button calls `utils.data_utils.SegyHeaderLoader` to process the uploaded file.
5.  **State Update & Rerun:** Upon successful completion of a logic step, the page module updates `st.session_state` with new data and changes the `current_step` value to transition the user to the next stage of the workflow. It then calls `st.rerun()` to make the `app.py` router re-evaluate the `current_step` and render the new page, completing the cycle.

This modular, state-driven flow is a significant improvement over a monolithic script, as it breaks down a complex process into manageable, decoupled components that are easier to develop, debug, and maintain.

## 2. Detailed File-by-File Breakdown

---

## `app.py`

### ### `app.py`

*   **Purpose:** Serves as the main entry point and central router for the multi-page Streamlit application.
*   **Key Components:**
    *   `initialize_gpu_functions()`: A cached function that attempts to import GPU-specific computation functions from `utils` and sets a global `GPU_AVAILABLE` flag. This ensures that computationally intensive tasks can leverage hardware acceleration where possible, while allowing the app to fall back gracefully.
    *   `APP_STEPS`: A dictionary that defines the navigation structure, mapping user-friendly step names to internal state identifiers (e.g., `"1. Load Data": "load_data"`).
    *   **Sidebar Navigation Logic:** A `st.sidebar.radio` element that controls the main application flow by updating `st.session_state.current_step`.
    *   **Routing Block:** A series of `if/elif` statements that check `st.session_state.current_step` and call the appropriate `render()` function from the corresponding module in the `pages/` directory.
    *   `reset_state()`: A call to a function in `common.session_state` triggered by a "Start New Analysis" button, which clears all session data to begin a fresh workflow.
*   **Dependencies:**
    *   **Internal:** `common.constants`, `common.session_state`, and all `render` functions from the `pages/` directory. It also conditionally imports from `utils.dlogst_spec_descriptor_gpu`.
    *   **External:** `streamlit`, `logging`, `torch`.
*   **Refactoring Rationale:** This file exemplifies the "Controller" or "Router" pattern. By centralizing navigation and workflow control, it decouples the individual pages from one another. A page doesn't need to know which page comes next; it simply performs its task and sets a state (`analysis_complete = True`). `app.py` then uses this state to determine the next view. This is vastly more maintainable than a monolithic script with deeply nested conditional logic.

---

## `common/`

### ### `common/constants.py`

*   **Purpose:** Centralizes all static, constant values used across the application.
*   **Key Components:**
    *   `APP_TITLE`: The application's title string.
    *   `AVAILABLE_OUTPUTS_*`: Lists defining the different types of analysis outputs available for various modes (e.g., single trace vs. section).
    *   `EXPORTABLE_ATTR_*`: Lists and mappings that define which calculated attributes can be exported, along with their user-facing display names.
    *   `DESCRIPTOR_LIMITS`: A dictionary specifying default minimum and maximum values for plotting various spectral descriptors, ensuring consistent visualization scales.
*   **Dependencies:** None.
*   **Refactoring Rationale:** Consolidating constants into a single file is a core principle of clean code. In a monolith, these "magic strings" and "magic numbers" would be scattered throughout the code, making updates difficult and error-prone. This module ensures that a change to a value (like an output name) only needs to be made in one place, guaranteeing consistency across the entire application.

### ### `common/session_state.py`

*   **Purpose:** Manages the lifecycle of the application's state stored in Streamlit's `session_state`.
*   **Key Components:**
    *   `initialize_session_state()`: Initializes all necessary keys in `st.session_state` with default `None` or empty values. This prevents `KeyError` exceptions and ensures a predictable state structure at application startup.
    *   `reset_state()`: A critical function that clears all user data, temporary files, and state variables from the session. It systematically deletes session keys and removes temporary files from disk, allowing a user to start a new analysis cleanly without restarting the server.
*   **Dependencies:**
    *   **Internal:** None.
    *   **External:** `streamlit`, `os`, `shutil`, `tempfile`, `logging`.
*   **Refactoring Rationale:** State management is a primary challenge in monolithic applications. This module abstracts all state initialization and reset logic. Instead of manually managing dozens of state variables at different points, other modules can simply call `initialize_session_state()` or `reset_state()`. This greatly simplifies the logic within pages and reduces the risk of state-related bugs, such as data from a previous session leaking into a new one.

### ### `common/ui_elements.py`

*   **Purpose:** Provides reusable, custom Streamlit UI components to ensure a consistent user experience and reduce code duplication.
*   **Key Components:**
    *   `slider_with_number_input()`: A composite widget that combines a `st.slider` and a `st.number_input` for a single parameter, offering both quick adjustment and precise input.
    *   `get_suggested_batch_size()`: A helper function that inspects available GPU memory (via `torch`) to suggest a reasonable batch size for processing, improving performance and preventing out-of-memory errors.
    *   `get_suggested_batch_size_for_export()`: A heuristic-based function to suggest a batch size for file exports, helping to manage output file sizes.
*   **Dependencies:**
    *   **Internal:** None.
    *   **External:** `streamlit`, `torch` (optional).
*   **Refactoring Rationale:** In a large application, you often need the same combination of UI widgets in multiple places. Without a module like this, you would copy and paste the same UI code, leading to inconsistencies and maintenance headaches. By creating reusable functions for common UI patterns, this module ensures that every part of the application that needs a specific element (like a synchronized slider and number input) gets an identical, tested, and maintainable component.

---

## `pages/`

### ### `pages/load_data_page.py`

*   **Purpose:** Renders the UI for the first step of the workflow: uploading SEG-Y and well data files.
*   **Key Components:**
    *   `render()`: The main function that lays out the UI for this page.
    *   **File Uploaders:** `st.sidebar.file_uploader` widgets for SEG-Y and optional well data (Excel) files.
    *   **Header Configuration:** `st.sidebar.number_input` widgets for users to specify the byte locations of key headers (inline, crossline, coordinates) in the SEG-Y file.
    *   **Load Data Logic:** An `if st.sidebar.button("Load Data")` block that, upon click, uses `utils.data_utils.SegyHeaderLoader` to process the uploaded file, stores the loader object and file metadata in `st.session_state`, and transitions the app to the next step by setting `st.session_state.current_step = "configure_display"`.
*   **Dependencies:**
    *   **Internal:** `common.constants`, `common.session_state`, `common.ui_elements`, `utils.data_utils`.
    *   **External:** `streamlit`, `os`, `tempfile`, `logging`.
*   **Refactoring Rationale:** This module perfectly encapsulates the "Load Data" concern. In a monolith, the UI for file uploading, the logic for parsing headers, and the state transition would be intertwined with other application logic. Here, the file is self-contained. Its single responsibility is to get data from the user and hand it off to the backend (`utils`). This makes it easy to modify the data loading step (e.g., add support for a new file type) without affecting any other part of the application.

### ### `pages/configure_display_page.py`

*   **Purpose:** Renders the UI for configuring all analysis and visualization parameters before processing.
*   **Key Components:**
    *   `render()`: The main function that lays out the UI for this page.
    *   **Basemap & SEGY Info:** An expander that displays key information about the loaded SEG-Y file and provides controls for generating and showing a basemap using `utils.visualization.plot_basemap_with_wells`.
    *   **Parameter Configuration:** A large section with numerous sliders and number inputs (using `common.ui_elements.slider_with_number_input`) for setting spectral parameters, statistics settings, and plot limits.
    *   `calculate_stats_and_defaults()`: A call to a function in `utils.processing` that analyzes a sample of the data to provide intelligent default values for the various parameters, which are then populated into the UI.
    *   **Confirm Settings Button:** A button that finalizes the parameter configuration, stores it in `st.session_state.plot_settings`, and transitions the app to the next step.
*   **Dependencies:**
    *   **Internal:** `common.session_state`, `common.ui_elements`, `utils.visualization`, `utils.data_utils`, `utils.processing`.
    *   **External:** `streamlit`, `segyio`, `logging`, `plotly.colors`, `numpy`.
*   **Refactoring Rationale:** This module isolates the complex task of parameter configuration. A monolithic script would likely have this configuration logic mixed with analysis and plotting code. By creating a dedicated page, we allow users to complete this distinct mental task in one place. Furthermore, it cleanly separates the "what" (the parameters chosen by the user) from the "how" (how those parameters are used in calculations), which resides in the `utils` directory.

### ### `pages/select_area_page.py`

*   **Purpose:** Renders the UI for selecting the specific area of the seismic data to be analyzed.
*   **Key Components:**
    *   `render()`: The main function that lays out the UI for this page.
    *   **Mode Selection:** A `st.sidebar.selectbox` that allows the user to choose the analysis mode (e.g., "By well markers", "Single inline", "By Polyline"). The UI dynamically changes based on this selection.
    *   **Mode-Specific UI:** Conditional blocks that render different UI elements based on the selected mode. For example, it shows inline/crossline number inputs for line modes, or a multiselect for well-marker pairs.
    *   **Data Selection Logic:** Each mode has a "Proceed" or "Next" button that triggers the logic to identify the relevant trace indices from the `header_loader` based on the user's selection. For instance, it filters the header DataFrame for a specific inline number or finds traces near a polyline using `utils.general_utils.find_traces_near_polyline`.
    *   **State Update:** Once the traces are identified, their indices are stored in `st.session_state.selected_indices`, a flag `st.session_state.area_selected` is set to `True`, and the app transitions to the analysis step.
*   **Dependencies:**
    *   **Internal:** `common.constants`, `common.session_state`, `common.ui_elements`, `utils.data_utils`, `utils.general_utils`.
    *   **External:** `streamlit`, `numpy`, `pandas`, `logging`.
*   **Refactoring Rationale:** This module handles the complex, multi-pathed logic of area selection. In a monolith, this would be a convoluted series of `if/else` statements. By modularizing it, each selection mode's UI and logic are contained within their own conditional block, making them easy to understand and modify independently. For example, adding a new selection mode would simply require adding a new option to the selectbox and a corresponding `elif` block, with no impact on the existing modes.

### ### `pages/analyze_data_page.py`

*   **Purpose:** Orchestrates the main data processing and analysis, and displays the results.
*   **Key Components:**
    *   `render()`: The main function that acts as a sub-router for this page, directing to different rendering functions based on the analysis mode.
    *   `render_aoi_export()`: A specialized function to handle the configuration and execution of large-scale AOI (Area of Interest) exports.
    *   **Analysis Logic:** Contains the core loops that iterate over selected traces (e.g., from `st.session_state.selected_indices`).
    *   **Computation Calls:** Invokes the heavy-lifting computational functions from `utils.processing` (like `calculate_woss`) and `utils.dlogst_spec_descriptor_gpu` to perform the spectral analysis on the trace data.
    *   **Visualization Calls:** Once analysis is complete, it uses plotting functions from `utils.visualization` (like `plot_trace_with_descriptors` or `plot_descriptor_section`) to generate and display the output figures.
    *   **State Update:** Stores the calculated descriptors and figures in `st.session_state` and sets `st.session_state.analysis_complete = True`.
*   **Dependencies:**
    *   **Internal:** `common.constants`, `common.session_state`, `common.ui_elements`, `utils.data_utils`, `utils.general_utils`, `utils.processing`, `utils.visualization`, `utils.gpu_utils`.
    *   **External:** `streamlit`, `numpy`, `pandas`, `logging`, `segyio`, `tqdm`.
*   **Refactoring Rationale:** This module is the heart of the application's backend execution, but it's still primarily an orchestrator. Its job is to prepare the data and then pass it to the specialized `utils` functions for computation and visualization. This separation is crucial. The `analyze_data_page` doesn't need to know the mathematical details of a spectral descriptor; it just needs to know which function to call from `utils` to get the result. This makes the analysis workflow easy to read and allows the complex algorithms in `utils` to be developed and tested in isolation.

### ### `pages/export_results_page.py`

*   **Purpose:** Renders the UI for viewing final results and exporting them to files.
*   **Key Components:**
    *   `render()`: A sub-router that calls the appropriate rendering function based on the current sub-step (`view_results`, `export_process`, etc.).
    *   `render_view_results()`: Displays the plots and data generated in the analysis step.
    *   `render_export_configuration()`: Provides UI for the user to select which attributes to export and how to batch them (e.g., by inline or crossline).
    *   `render_export_process()`: The function that executes the export. It iterates through the data in batches, calculates the required attributes, and writes them to new SEG-Y files in a temporary directory using `segyio`.
    *   `render_download_export()`: Once the export process is complete, this function creates a zip archive of the generated SEG-Y files and provides a `st.download_button` for the user.
*   **Dependencies:**
    *   **Internal:** `common.constants`, `common.session_state`, `common.ui_elements`, `utils.data_utils`, `utils.processing`, `utils.visualization`, `utils.gpu_utils`.
    *   **External:** `streamlit`, `os`, `tempfile`, `shutil`, `zipfile`, `logging`, `numpy`, `pandas`, `segyio`.
*   **Refactoring Rationale:** This module isolates the "post-analysis" concerns of viewing and exporting. The export logic, in particular, is complex, involving batching, file I/O, and data formatting. Encapsulating it here prevents the `analyze_data_page` from becoming bloated. A user might want to re-run an analysis with different parameters without exporting, or export the same result in multiple formats. This modularity supports such flexible workflows, which would be difficult to implement in a linear, monolithic script.

---

## `utils/`

### ### `utils/data_utils.py`

*   **Purpose:** Provides a suite of functions for loading, parsing, and handling seismic and well data.
*   **Key Components:**
    *   `SegyHeaderLoader`: A class that encapsulates all the logic for reading a SEG-Y file, parsing its trace headers according to user-specified byte locations, and handling coordinate scaling. It stores the header data (inlines, crosslines, coordinates) as NumPy arrays for efficient access.
    *   `load_excel_data()`: A function to load well marker data from an Excel file into a Pandas DataFrame, with validation for required columns.
    *   `get_nearest_trace_index()`: A geometric utility function that finds the seismic trace closest to a given X,Y coordinate, essential for linking well data to seismic data.
    *   `get_sampling_interval()` & `get_trace_count()`: Helper functions that use `segyio` to quickly extract basic metadata from a SEG-Y file.
    *   `merge_segy_batch_files()`: A utility to combine multiple SEG-Y files (created during a batched export) into a single, final file.
*   **Dependencies:**
    *   **Internal:** None.
    *   **External:** `segyio`, `numpy`, `pandas`, `tqdm`, `tkinter` (optional).
*   **Refactoring Rationale:** This module is a prime example of the Single Responsibility Principle. It is solely concerned with data access and parsing. Any other part of the application that needs to interact with raw data files does so through this module's well-defined API (e.g., `SegyHeaderLoader`). This is a massive improvement over a monolith where file I/O and parsing code might be duplicated and scattered, making it a nightmare to update if, for example, the format of an input file changes.

### ### `utils/dlogst_spec_descriptor_cpu.py`

*   **Purpose:** Provides a pure Python/NumPy implementation of the spectral descriptor calculations.
*   **Key Components:**
    *   `dlogst_spec_descriptor_cpu()`: The main function that takes a 1D seismic trace and various parameters, and returns a dictionary of calculated spectral descriptors (e.g., `spec_slope`, `hfc`, `fdom`). It uses `numpy` for array operations and `scipy.signal` for spectral analysis.
*   **Dependencies:**
    *   **Internal:** None.
    *   **External:** `numpy`, `scipy.signal`, `logging`.
*   **Refactoring Rationale:** This module serves as a crucial fallback and a clear reference implementation. By isolating the CPU-based computation, it allows the application to function on systems without a compatible GPU. It also provides a baseline for testing and validation against the GPU version. In a monolithic design, the CPU and GPU logic would likely be tangled together with `#ifdef`-style conditional blocks, making the code unreadable and difficult to debug. This clean separation is far superior.

### ### `utils/dlogst_spec_descriptor_gpu.py`

*   **Purpose:** Provides a highly optimized CuPy implementation of the spectral descriptor calculations for massive performance gains on compatible NVIDIA GPUs.
*   **Key Components:**
    *   `dlogst_spec_descriptor_gpu()`: A function for processing a single 1D trace on the GPU.
    *   `dlogst_spec_descriptor_gpu_2d_chunked()`: The key workhorse function for processing large datasets. It takes a 2D array (a batch of traces), performs the FFT and all subsequent calculations entirely on the GPU, and returns the results. It is optimized to use pinned memory for faster CPU-to-GPU data transfers.
    *   `dlogst_spec_descriptor_gpu_2d_chunked_mag()`: A variant of the 2D function that likely returns additional magnitude-related outputs.
    *   **Helper Functions (`calculate_slope`, `calculate_decrease`):** Internal helper functions that are also implemented using CuPy to ensure the entire computation pipeline remains on the GPU.
*   **Dependencies:**
    *   **Internal:** None.
    *   **External:** `cupy`, `numpy`, `logging`.
*   **Refactoring Rationale:** This module encapsulates the most complex and performance-critical part of the application. High-performance computing code (like CUDA/CuPy) is notoriously difficult to write and debug. By isolating it in its own module, it can be developed, optimized, and tested by specialists without interfering with the rest of the application's development. The rest of the app simply calls a function and gets a result, abstracting away the immense complexity of the parallel computation happening under the hood.

### ### `utils/export_utils.py`

*   **Purpose:** Provides helper functions specifically related to the data export process.
*   **Key Components:**
    *   `select_export_attributes()`: A function that filters a list of desired export attributes against what is actually available in a calculated descriptor dictionary. This ensures the export process doesn't fail by trying to write an attribute that wasn't calculated.
*   **Dependencies:**
    *   **Internal:** None.
    *   **External:** `numpy`.
*   **Refactoring Rationale:** While small, this module adheres to the principle of separating concerns. The logic for deciding which attributes are valid for export is a distinct responsibility. Placing it here keeps the main `export_results_page` cleaner and more focused on the orchestration of the export workflow (UI, batching, file I/O), while this utility handles a specific data validation rule.

### ### `utils/general_utils.py`

*   **Purpose:** Contains miscellaneous utility functions that don't fit into the more specialized categories like `data_utils` or `processing`.
*   **Key Components:**
    *   `parse_polyline_string()`: A robust parser for converting a user-provided string of coordinates into a list of vertices, handling multiple delimiters and formats.
    *   `distance_point_to_segment()`: A core geometric function to calculate the shortest distance from a point to a line segment.
    *   `find_traces_near_polyline()`: Uses `distance_point_to_segment` to iterate through all seismic traces and find those that lie within a specified tolerance of a user-defined polyline.
*   **Dependencies:**
    *   **Internal:** None.
    *   **External:** `math`, `numpy`, `tqdm`.
*   **Refactoring Rationale:** This module is a classic "utility belt." It holds general-purpose, reusable functions. In a monolith, these helper functions might be defined locally within the larger function that needs them, leading to code duplication if another part of the application needs the same functionality. By placing them in a central, general-purpose utility module, they can be reused anywhere, are easy to find, and can be tested independently.