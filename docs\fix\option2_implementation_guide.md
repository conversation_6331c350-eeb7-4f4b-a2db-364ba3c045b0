# Implementation Guide for Option 2 (Single Inline - All Crosslines)

This guide provides step-by-step instructions for implementing Option 2 (Single Inline - All Crosslines) in the WOSS Seismic Analysis Tool.

## Step 1: Update analyze_data_page.py

Add a new section to handle the "Single inline (all crosslines)" mode in the render() function:

1. Open `pages/analyze_data_page.py`
2. Find the section after the "Well Markers Mode" (around line 565)
3. Add the following code for the "Single inline (all crosslines)" mode:

```python
# Single Inline Mode
elif st.session_state.selection_mode == "Single inline (all crosslines)":
    st.subheader("Selected Area Information")
    st.write(f"Selected inline: {st.session_state.selected_inline}")
    st.write(f"Number of selected traces: {len(st.session_state.selected_indices)}")

    # Check if we have loaded trace data
    loaded_trace_data = st.session_state.get('loaded_trace_data', [])
    
    if not loaded_trace_data:
        # Load trace data for the selected inline
        with st.spinner(f"Loading traces for inline {st.session_state.selected_inline}..."):
            loaded_trace_data = []
            max_len = 0
            try:
                for trace_idx in st.session_state.selected_indices:
                    # Load the trace sample
                    trace_sample = load_trace_sample(st.session_state.header_loader.source_file_path, trace_idx)
                    loaded_trace_data.append({'trace_sample': trace_sample, 'trace_idx': trace_idx})
                    max_len = max(max_len, len(trace_sample))
                
                # Pad traces if necessary
                if max_len > 0:
                    for item in loaded_trace_data:
                        if len(item['trace_sample']) < max_len:
                            pad_width = max_len - len(item['trace_sample'])
                            item['trace_sample'] = np.pad(item['trace_sample'], (0, pad_width), 'constant')
                
                st.session_state.loaded_trace_data = loaded_trace_data
                st.success(f"Successfully loaded {len(loaded_trace_data)} traces for inline {st.session_state.selected_inline}.")
            except Exception as e:
                st.error(f"Error loading traces: {e}")
                logging.error(f"Error loading traces for inline {st.session_state.selected_inline}: {e}", exc_info=True)
    else:
        st.success(f"Loaded {len(loaded_trace_data)} traces for analysis.")

    # Add a button to calculate descriptors
    st.markdown("---")
    st.markdown("### Next Steps:")
    st.markdown("1. Click **Calculate Descriptors** to process the selected traces and compute signal descriptors")
    st.markdown("2. Review the statistical summary that will appear")
    st.markdown("3. Click **View Results** to proceed to the results page for visualization and analysis options")

    if not st.session_state.get('GPU_AVAILABLE', False):
        st.error("GPU processing is required for this analysis mode, but no GPU is available. Please check your system configuration.")
    elif st.button("Calculate Descriptors", key="calculate_descriptors_button_inline", help="Process the selected traces and compute spectral descriptors"):
        if not loaded_trace_data:
            st.warning("No traces loaded to calculate descriptors.")
            return

        # Stack traces into a 2D array
        trace_samples = [item['trace_sample'] for item in loaded_trace_data]
        max_len = max(len(trace) for trace in trace_samples)
        padded_traces = [np.pad(trace, (0, max_len - len(trace)), 'constant') for trace in trace_samples]
        traces_array = np.stack(padded_traces).astype(np.float32)

        # Get descriptor settings from plot_settings
        descriptor_settings = {
            'use_band_limited': st.session_state.plot_settings.get('use_band_limited', False),
            'shape': st.session_state.plot_settings.get('shape', 0.35),
            'kmax': st.session_state.plot_settings.get('kmax', 120.0),
            'int_val': st.session_state.plot_settings.get('int_val', 35.0),
            'b1': st.session_state.plot_settings.get('b1', 5.0),
            'b2': st.session_state.plot_settings.get('b2', 40.0),
            'p_bandwidth': st.session_state.plot_settings.get('p_bandwidth', 2.0),
            'roll_percent': st.session_state.plot_settings.get('roll_percent', 0.80),
        }

        # Calculate descriptors using GPU
        progress_bar = st.progress(0)
        status_text = st.empty()
        status_text.text("Initializing descriptor calculation...")

        with st.spinner("Calculating spectral descriptors using GPU..."):
            try:
                # Use the 2D chunked GPU function
                fmax_calc = max_len // 2 if max_len > 0 else 250
                calculated_descriptors_dict = dlogst_spec_descriptor_gpu_2d_chunked(
                    traces_array,
                    st.session_state.dt,
                    fmax=fmax_calc,
                    batch_size=st.session_state.get('batch_size', 512),
                    **descriptor_settings
                )

                # Calculate WOSS if needed
                if 'hfc' in calculated_descriptors_dict and 'norm_fdom' in calculated_descriptors_dict and 'mag_voice_slope' in calculated_descriptors_dict:
                    # Get HFC p95 from session state
                    hfc_p95 = st.session_state.get('hfc_p95', 1.0)
                    
                    # Create WOSS parameters
                    woss_params = {
                        'hfc_p95': hfc_p95,
                        'epsilon': st.session_state.plot_settings.get('epsilon', 1e-4),
                        'fdom_exponent': st.session_state.plot_settings.get('fdom_exponent', 2.0)
                    }
                    
                    # Calculate WOSS for each trace
                    woss_array = np.zeros_like(calculated_descriptors_dict['hfc'])
                    for i in range(traces_array.shape[0]):
                        trace_components = {
                            'hfc': calculated_descriptors_dict['hfc'][i],
                            'norm_fdom': calculated_descriptors_dict['norm_fdom'][i],
                            'mag_voice_slope': calculated_descriptors_dict['mag_voice_slope'][i]
                        }
                        woss_array[i] = calculate_woss(trace_components, woss_params)
                    
                    # Add WOSS to the calculated descriptors
                    calculated_descriptors_dict['WOSS'] = woss_array

                # Store the calculated descriptors in session state
                st.session_state.calculated_descriptors = calculated_descriptors_dict
                st.session_state.analysis_complete = True
                st.success("Descriptor calculation complete.")
                
                # Add a button to view results
                st.markdown("---")
                st.success("✅ Descriptor calculation is complete. You can now view the detailed results.")
                if st.button("View Results", key="view_results_button_inline", help="Open the detailed analysis report"):
                    st.session_state.current_step = "view_results"
                    st.rerun()
                
            except Exception as e:
                st.error(f"Error calculating descriptors: {e}")
                logging.error(f"Error calculating descriptors for inline {st.session_state.selected_inline}: {e}", exc_info=True)
                return
```

## Step 2: Update export_results_page.py

Add or update the render_view_results() function to handle the "Single inline (all crosslines)" mode:

1. Open `pages/export_results_page.py`
2. Find the render_view_results() function (or add it if it doesn't exist)
3. Add the following code for the "Single inline (all crosslines)" mode:

```python
def render_view_results():
    """Render the view results page UI."""
    st.header("Step 5: View Results")

    # Check if we have calculated descriptors
    if not st.session_state.get('calculated_descriptors'):
        st.warning("No calculated descriptors found. Please go back to Step 4 and calculate descriptors.")
        if st.button("Back to Analyze Data"):
            st.session_state.current_step = "analyze_data"
            st.rerun()
        return

    # Display different visualizations based on the selection mode
    if st.session_state.selection_mode == "By well markers":
        # Existing code for well markers mode
        # ...
    elif st.session_state.selection_mode == "Single inline (all crosslines)":
        st.subheader(f"Inline {st.session_state.selected_inline} Section")

        # Allow the user to select which descriptors to display
        available_outputs = list(st.session_state.calculated_descriptors.keys())
        selected_output = st.selectbox("Select descriptor to display:", available_outputs)

        # Get the descriptor data
        descriptor_data = st.session_state.calculated_descriptors[selected_output]

        # Get crossline numbers for x-axis
        crossline_numbers = None
        if st.session_state.header_loader:
            # Create a mask for the selected inline
            inline_mask = st.session_state.header_loader.inlines == st.session_state.selected_inline
            # Get the corresponding crossline numbers
            crossline_numbers = st.session_state.header_loader.crosslines[inline_mask]

        # Create the section plot
        fig = plot_multi_trace_section(
            trace_data_list=[],  # Not needed for section plot
            time_vector=np.arange(descriptor_data.shape[1]) * st.session_state.dt,
            descriptors_list=[],  # Not needed for section plot
            plot_settings=st.session_state.plot_settings,
            output_type=selected_output,
            title=f"{selected_output} - Inline {st.session_state.selected_inline}",
            x_axis_values=crossline_numbers,  # Use crossline numbers for x-axis
            x_axis_title="Crossline"
        )

        # Display the plot
        st.plotly_chart(fig, use_container_width=True)
```

## Step 3: Test the Implementation

1. Run the application
2. Navigate to Step 3 (Select Area)
3. Select "Single inline (all crosslines)" mode
4. Enter an inline number and click "Next: Analyze Data"
5. In the Analyze Data page, click "Calculate Descriptors"
6. After calculation is complete, click "View Results"
7. In the View Results page, select a descriptor to display and verify that the section plot is displayed correctly

## Step 4: Troubleshooting

If you encounter any issues during implementation or testing, check the following:

1. Make sure the GPU functions are available and working correctly
2. Check the logs for any error messages
3. Verify that the trace data is loaded correctly
4. Ensure that the descriptor calculation is working correctly
5. Check that the section plot is displayed correctly

## Additional Notes

- The implementation assumes that the GPU functions are available and working correctly. If not, you may need to add fallback logic for CPU processing.
- The implementation assumes that the plot_multi_trace_section function is already implemented and can handle 2D arrays of descriptors correctly.
- The implementation assumes that the calculate_woss function is already implemented and can calculate WOSS for a single trace.
