# Array Orientation Conventions for WOSS Seismic Analysis

## Overview

This document describes the correct array orientation conventions used throughout the WOSS Seismic Analysis application to ensure proper visualization of seismic sections.

## Problem Background

The seismic section plots in Step 5 (View Analysis Results) were displaying with incorrect orientation due to inconsistent array handling between data processing and visualization functions.

## Array Orientation Standards

### 1. GPU Processing Functions Output Format

**Functions:** `dlogst_spec_descriptor_gpu_2d_chunked()`, `dlogst_spec_descriptor_gpu_2d_chunked_mag()`
**Location:** `utils/dlogst_spec_descriptor_gpu.py`

**Output Format:** `(traces, time_samples)`
- **Dimension 0:** Number of traces (spatial dimension)
- **Dimension 1:** Number of time samples (temporal dimension)

**Example:**
```python
# For inline analysis with 50 crosslines and 1000 time samples
descriptor_data.shape = (50, 1000)  # (traces, time_samples)
```

### 2. Visualization Function Input/Output Format

**Function:** `plot_section_2d()`
**Location:** `utils/visualization.py`

**Input Format:** `(traces, time_samples)` - as received from GPU functions
**Internal Processing:** Transposes to `(time_samples, traces)` for proper seismic visualization
**Visualization Format:** `(time_samples, traces)`

**Rationale:** 
- Seismic sections follow the convention where:
  - X-axis represents spatial dimension (trace positions)
  - Y-axis represents time dimension (increasing downward)
- This matches the reference tkinter implementation

### 3. Data Flow Through Application

```
1. SEG-Y Loading → Individual traces
2. GPU Processing → (traces, time_samples) arrays
3. Analysis/Export → Maintains (traces, time_samples) format
4. Visualization → Transposes to (time_samples, traces) for display
```

## Key Functions and Their Roles

### Analysis Functions (`pages/analyze_data_page.py`)
- `calculate_line_descriptors()`: Calls GPU functions, stores results as `(traces, time_samples)`
- `export_inline_results()`: Exports data maintaining `(traces, time_samples)` format

### Visualization Functions (`pages/export_results_page.py`)
- `render_inline_analysis_results()`: Passes `(traces, time_samples)` data to visualization
- `render_view_results()`: Handles both inline and crossline section visualization

### Core Visualization (`utils/visualization.py`)
- `plot_section_2d()`: Accepts `(traces, time_samples)`, transposes for display

## Fixed Issues

### Before Fix
- Ambiguous orientation detection logic that could incorrectly transpose data
- Inconsistent handling when number of traces equals number of time samples
- Data appeared transposed in seismic sections

### After Fix
- Clear, consistent orientation handling
- Always expects `(traces, time_samples)` input from GPU functions
- Always transposes to `(time_samples, traces)` for visualization
- Proper seismic section display with correct spatial-temporal relationships

## Testing Guidelines

To verify the fixes work correctly:

1. **Inline Analysis Test:**
   - Run inline analysis (Step 4)
   - Export results and view in Step 5
   - Verify: X-axis shows crossline numbers, Y-axis shows time (increasing downward)

2. **Crossline Analysis Test:**
   - Run crossline analysis (Step 4)
   - Export results and view in Step 5
   - Verify: X-axis shows inline numbers, Y-axis shows time (increasing downward)

3. **Data Orientation Check:**
   - Spectral descriptors should display with proper spatial continuity
   - Time axis should increase downward (seismic convention)
   - No apparent transposition or flipping of the data

## Code Examples

### Correct Usage in Analysis
```python
# GPU function returns (traces, time_samples)
all_descriptors = dlogst_spec_descriptor_gpu_2d_chunked(
    section_data_2d,  # Shape: (n_traces, n_time_samples)
    dt, **params
)

# Store maintaining the same orientation
calculated_descriptors = {key: all_descriptors[key] for key in export_attributes}
```

### Correct Usage in Visualization
```python
# Pass data as (traces, time_samples) to visualization
fig = plot_section_2d(
    section_data=descriptor_data,  # Shape: (traces, time_samples)
    time_vector=time_vector,
    plot_settings=plot_settings,
    output_type=selected_descriptor
)
```

## Future Development Notes

- Always maintain `(traces, time_samples)` format for data storage and transfer
- Only transpose to `(time_samples, traces)` at the final visualization step
- When adding new visualization functions, follow the same convention
- Document any deviations from this standard clearly

## Related Files

- `utils/dlogst_spec_descriptor_gpu.py` - GPU processing functions
- `utils/visualization.py` - Core visualization functions
- `pages/analyze_data_page.py` - Analysis workflow
- `pages/export_results_page.py` - Results visualization
- `backup_tk/3D_WOSS_Main_Script_init.py` - Reference implementation
